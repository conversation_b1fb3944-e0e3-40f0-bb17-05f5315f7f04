<template>
  <TransitionGroup name="tab-item" tag="div" class="category-tabs-list">
    <div
      v-for="(category, index) in categories"
      :key="category.key"
      :class="[
              'category-tab-item',
              {
                'active': index === activeIndex,
                'disabled': category.disabled
              }
            ]"
      @click="handleTabClick(category, index)"
    >
      <!-- Circular Icon Container -->
      <div class="category-icon-container">
        <div
          class="category-icon-circle"
          :class="{ 'active': index === activeIndex }"
          :style="getCircleStyle(category, index === activeIndex)"
        >
          <div
            class="category-icon-inner"
            :style="getInnerGradientStyle(category)"
          >
            <div class="category-icon">
              <!-- 12px x 12px placeholder -->
              <div class="icon-placeholder"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Category Label -->
      <span class="category-label">{{ category.label }}</span>
    </div>
  </TransitionGroup>
</template>

<script setup>
// No imports needed for simplified version

defineOptions({
  name: 'CategoryTabs'
})
// Props
defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  activeIndex: {
    type: Number,
    default: 0
  },
})

// Emits
const emit = defineEmits(['tab-click', 'tab-change', 'scroll'])

// Category to icon and gradient mapping
const categoryMappings = {
  // By label (Chinese)
  '全部': { gradient: 'blue', icon: 'grid' },
  '声光报警': { gradient: 'green', icon: 'switch' },
  '支架': { gradient: 'cyan', icon: 'screen' },
  '智能网关': { gradient: 'orange', icon: 'gateway' },
  '照明': { gradient: 'yellow', icon: 'light' },
  '安防': { gradient: 'red', icon: 'security' },
  '配件包': { gradient: 'purple', icon: 'environment' },
  '娱乐': { gradient: 'pink', icon: 'entertainment' },
  // By key patterns
  'all': { gradient: 'blue', icon: 'grid' },
  'switch': { gradient: 'green', icon: 'switch' },
  'screen': { gradient: 'cyan', icon: 'screen' },
  'gateway': { gradient: 'orange', icon: 'gateway' },
  'light': { gradient: 'yellow', icon: 'light' },
  'security': { gradient: 'red', icon: 'security' },
  'environment': { gradient: 'purple', icon: 'environment' },
  'entertainment': { gradient: 'pink', icon: 'entertainment' }
}

// Gradient color definitions matching Figma
const gradientMap = {
  blue: 'linear-gradient(180deg, #E6F7FF 0%, #69C0FF 100%)',
  green: 'linear-gradient(180deg, #F6FFED 0%, #95DE64 100%)',
  cyan: 'linear-gradient(180deg, #E6FFFB 0%, #5CDBD3 100%)',
  orange: 'linear-gradient(180deg, #FFF7E6 0%, #FF9C6E 100%)',
  yellow: 'linear-gradient(180deg, #FFFBE6 0%, #FADB14 100%)',
  red: 'linear-gradient(180deg, #FFF1F0 0%, #FF7875 100%)',
  purple: 'linear-gradient(180deg, #F9F0FF 0%, #B37FEB 100%)',
  pink: 'linear-gradient(180deg, #FFF0F6 0%, #FF85C0 100%)',
  default: 'linear-gradient(180deg, #F5F5F5 0%, #D9D9D9 100%)'
}

// Get category mapping based on key or label
const getCategoryMapping = (category) => {
  // Try key first, then label, then fallback
  const mapping = categoryMappings[category.key] ||
                  categoryMappings[category.label] ||
                  categoryMappings[category.key?.toLowerCase()] ||
                  { gradient: 'default', icon: 'default' }
  return mapping
}

// Get circle style with gradient and active state
const getCircleStyle = (category, isActive) => {
  const mapping = getCategoryMapping(category)
  const gradient = gradientMap[mapping.gradient]

  const baseStyle = {
    background: gradient
  }

  if (isActive) {
    baseStyle.border = '1px solid #BF8630'
    baseStyle.boxShadow = '0px 0px 0px 2px rgba(255, 251, 240, 1)'
  }

  return baseStyle
}

// Get inner gradient style
const getInnerGradientStyle = (category) => {
  const mapping = getCategoryMapping(category)
  return {
    background: gradientMap[mapping.gradient]
  }
}

// Icon components removed - using placeholder div instead

const handleTabClick = (category, index) => {
  if (category.disabled) return
  emit('tab-click', category, index)
  emit('tab-change', index)
}
</script>

<style scoped lang="stylus">
.category-tabs-container {
  position: relative;
  background: #ffffff;
  width: 100%;
}

.category-tabs-wrapper {
  width: 100%;
}

.category-tabs-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  grid-auto-flow: row;
}

.category-tab-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease-in-out;
  will-change: transform;
  justify-self: center;
}

.category-tab-item:hover:not(.disabled) {
  transform: translateY(-1px);
}

.category-tab-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Icon Container */
.category-icon-container {
  position: relative;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Circular Icon Background - Matching Figma exactly */
.category-icon-circle {
  position: relative;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
  will-change: transform, box-shadow;
  background: #FFFFFF; /* Fallback */
}

.category-icon-circle.active {
  transform: scale(1.05);
}

/* Inner gradient circle */
.category-icon-inner {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: inherit;
}

/* Icon styling */
.category-icon {
  position: relative;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.85);
  z-index: 1;
}

/* Icon placeholder - 12px x 12px */
.icon-placeholder {
  width: 18px;
  height: 18px;
  background: #FFFFFF;
  border-radius: 2px;
  display: block;
}

/* Category Label - Matching Figma typography */
.category-label {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 11px;
  font-weight: 400;
  line-height: 1.8181818181818181;
  text-align: center;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 48px;
  transition: all 0.3s ease-in-out;
}

/* Active state label */
.category-tab-item.active .category-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

/* Disabled state label */
.category-tab-item.disabled .category-label {
  opacity: 0.65;
}

/* Transitions for tab items */

/* Transitions */
.tab-item-enter-active,
.tab-item-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-item-enter-from {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.tab-item-leave-to {
  opacity: 0;
  transform: translateY(8px) scale(0.95);
}

.tab-item-move {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive design */
@media (max-width: 768px) {
  .category-tabs-list {
    gap: 6px;
    padding: 6px 8px;
  }

  .category-icon-container {
    width: 36px;
    height: 36px;
  }

  .category-icon-circle {
    width: 36px;
    height: 36px;
  }

  .category-icon-inner {
    width: 30px;
    height: 30px;
  }

  .category-icon {
    width: 16px;
    height: 16px;
  }

  .category-label {
    font-size: 10px;
    max-width: 42px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .category-icon-circle {
    background-size: 100% 100%;
  }
}

/* Focus states for accessibility */
.category-tab-item:focus-visible {
  outline: 2px solid #BF8630;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .category-tab-item,
  .category-icon-circle,
  .category-label,
  .tab-item-enter-active,
  .tab-item-leave-active {
    transition: none;
  }

  .category-tab-item:hover:not(.disabled) {
    transform: none;
  }

  .category-icon-circle.active {
    transform: none;
  }
}
</style>
