<template>
  <Transition name="grid-fade" mode="out-in">
    <div v-if="!hidden" class="category-grid">
      <TransitionGroup
        name="grid-item"
        tag="div"
        class="category-grid-wrapper"
      >
        <div
          v-for="(category, index) in categories"
          :key="category.id"
          :class="[
              'category-grid-item',
              {
                'active': category.id === activeKey,
                'loading': loading
              }
            ]"
          :style="{ '--delay': index * 50 + 'ms' }"
          @click="handleCategoryClick(category)"
        >
          <div class="category-content">
            <div v-if="category.icon" class="category-icon">
              <Icon :icon="category.icon" />
            </div>
            <span class="category-label">{{ category.label }}</span>
            <div v-if="category.count" class="category-count">
              {{ category.count }}
            </div>
          </div>
          <div class="category-ripple"></div>
        </div>
      </TransitionGroup>
    </div>
  </Transition>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import Icon from '@/components/icon/icon.vue'

// Component definition
defineOptions({
  name: 'CategoryGrid'
})

// Props
const props = defineProps({
  categories: {
    type: Array,
    default: () => [],
    validator: (categories) => {
      return categories.every(cat => 
        cat && typeof cat === 'object' && 
        cat.key && cat.label
      )
    }
  },
  activeKey: {
    type: [String, Number],
    default: null
  },
  columns: {
    type: Number,
    default: 3,
    validator: (value) => value > 0 && value <= 6
  },
  loading: {
    type: Boolean,
    default: false
  },
  hidden: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['category-select'])

// Methods
const handleCategoryClick = (category) => {
  if (props.loading || category.disabled) return
  
  emit('category-select', category)
}
</script>

<style scoped>
.category-grid {
  width: 100%;
  padding-top: 12px;
}

/* Fixed 3-column grid layout with Figma spacing */
.category-grid-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: 100%;
}

/* Grid item matching Figma design exactly */
.category-grid-item {
  position: relative;
  padding: 2px;
  background: #F5F5F5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  min-height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  height: 24px;
  will-change: transform, box-shadow;
}

/* Hover state - subtle effect */
.category-grid-item:hover:not(.active):not(.loading) {
  background: #EEEEEE;
}

/* Active/selected state matching Figma */
.category-grid-item.active {
  background: #FFF7E6;
  color: #BF8630;
}

/* Loading state */
.category-grid-item.loading {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Content layout - simplified */
.category-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0;
  position: relative;
  z-index: 1;
  width: 100%;
}

/* Icon styling - hidden for now to match Figma */
.category-icon {
  display: none;
}

/* Typography matching Figma exactly */
.category-label {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 12px;
  font-weight: 400;
  text-align: center;
  line-height: 1.67;
  transition: all 0.3s ease-in-out;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.category-grid-item.active .category-label {
  font-weight: 500;
  color: #BF8630;
}

/* Count badge - hidden for now to match Figma */
.category-count {
  display: none;
}

/* Ripple effect for click feedback */
.category-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease-out;
  pointer-events: none;
}

.category-grid-item:active .category-ripple {
  width: 120px;
  height: 120px;
}

/* Enhanced transitions for smooth animations */
.grid-fade-enter-active,
.grid-fade-leave-active {
  transition: all 0.4s ease-in-out;
}

.grid-fade-enter-from,
.grid-fade-leave-to {
  opacity: 0;
  transform: translateY(-12px);
}

.grid-item-enter-active {
  transition: all 0.4s ease-in-out;
  transition-delay: var(--delay);
}

.grid-item-leave-active {
  transition: all 0.3s ease-in-out;
}

.grid-item-enter-from {
  opacity: 0;
  transform: translateY(12px) scale(0.9);
}

.grid-item-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/*
  Fixed 3-column layout - no responsive breakpoints
  Maintains consistent grid structure across all screen sizes
*/
</style>
